import { ChangeControl } from "../../models";

export const dummyChangeControl: ChangeControl[] = [
    {
        id: 1,
        projectId: 1,
        srNo: 1,
        dateLogged: "2024-01-15",
        originator: "<PERSON><PERSON> <PERSON>",
        description: "Modification in sewage treatment plant capacity from 5 MLD to 7 MLD due to population projection updates",
        costImpact: "High - ₹1.5 Crore",
        timeImpact: "3 months delay",
        resourcesImpact: "Additional civil work team required",
        qualityImpact: "Redesign of aeration tanks needed",
        changeOrderStatus: "Approved",
        clientApprovalStatus: "Approved",
        claimSituation: "Variation claim submitted"
    },
    {
        id: 2,
        projectId: 1,
        srNo: 2,
        dateLogged: "2024-01-20",
        originator: "<PERSON><PERSON><PERSON> <PERSON>",
        description: "Change in water treatment process - Addition of advanced oxidation process",
        costImpact: "Medium - ₹75 Lakhs",
        timeImpact: "2 months delay",
        resourcesImpact: "Specialized equipment installation team needed",
        qualityImpact: "Improved water quality parameters",
        changeOrderStatus: "Under Review",
        clientApprovalStatus: "Pending",
        claimSituation: "To be evaluated"
    }
];
