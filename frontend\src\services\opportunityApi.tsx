import { axiosInstance } from './axiosConfig';
import {
  OpportunityTracking,
  normalizeOpportunityTracking,
  prepareOpportunityTrackingForSubmission
} from '../models/opportunityTrackingModel';
import { OpportunityHistory } from '../models/opportunityHistoryModel';

type OpportunityStage = 'A' | 'B' | 'C' | 'D' | 'E';
type OpportunityTrackingStatus =
  'Bid Under Preparation' |
  'Bid Submitted' |
  'Bid Rejected' |
  'Bid Accepted'


// Backend-specific model for sending data
export interface BackendOpportunityTracking {
  id?: number;
  projectId?: number | null;
  stage: number;
  strategicRanking: string;
  bidManagerId?: string;
  reviewManagerId?: string;
  approvalManagerId?: string;
  operation: string;
  workName: string;
  client: string;
  clientSector: string;
  likelyStartDate: string;
  status: number;
  currency: string;
  capitalValue: number;
  durationOfProject: number;
  fundingStream: string;
  contractType: string;

  // Optional fields
  bidFees: number;
  emd: number;
  formOfEMD?: string;
  contactPersonAtClient?: string;
  dateOfSubmission?: string;
  percentageChanceOfProjectHappening?: number;
  percentageChanceOfNJSSuccess?: number;
  likelyCompetition?: string;
  grossRevenue: number;
  netNJSRevenue: number;
  followUpComments?: string;
  notes?: string;
  probableQualifyingCriteria?: string;
  currentHistory?: OpportunityHistory[];

  // Audit fields
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}

// Mapping functions to convert frontend types to backend numeric values
const mapStageToBackend = (stage: OpportunityStage | undefined): number => {
  switch (stage) {
    case 'A': return 1;
    case 'B': return 2;
    case 'C': return 3;
    case 'D': return 4;
    case 'E': return 5;
    default: return 1;
  }
};

const mapStageFromBackend = (stage: number): OpportunityStage => {
  switch (stage) {
    case 1: return 'A';
    case 2: return 'B';
    case 3: return 'C';
    case 4: return 'D';
    case 5: return 'E';
    default: return 'A';
  }
};

const mapStatusToBackend = (status: OpportunityTrackingStatus | undefined): number => {
  switch (status) {
    case 'Bid Under Preparation': return 0;
    case 'Bid Submitted': return 1;
    case 'Bid Rejected': return 2;
    case 'Bid Accepted': return 3;
    default: return 0;
  }
};

const mapStatusFromBackend = (status: number): OpportunityTrackingStatus => {
  switch (status) {
    case 0: return 'Bid Under Preparation';
    case 1: return 'Bid Submitted';
    case 2: return 'Bid Rejected';
    case 3: return 'Bid Accepted';
    default: return 'Bid Under Preparation';
  }
};

export const opportunityApi = {
  convertStringToNumberId: (id: string | number): number =>
    typeof id === 'string' ? parseInt(id, 10) : id,

  create: async (opportunityData: Partial<OpportunityTracking>): Promise<OpportunityTracking> => {
    try {
      if (!opportunityData.bidManagerId) {
        throw new Error('Bid Manager ID is required');
      }

      const normalizedData = normalizeOpportunityTracking(opportunityData);
      const preparedData = prepareOpportunityTrackingForSubmission(normalizedData);

      const command: BackendOpportunityTracking = {
        stage: mapStageToBackend(preparedData.stage as OpportunityStage || 'A'),
        strategicRanking: preparedData.strategicRanking || 'M',
        bidManagerId: preparedData.bidManagerId,
        approvalManagerId: preparedData.approvalManagerId || undefined,
        reviewManagerId: preparedData.reviewManagerId || undefined,
        operation: preparedData.operation || '',
        workName: preparedData.workName || '',
        client: preparedData.client || '',
        clientSector: preparedData.clientSector || '',
        likelyStartDate: preparedData.likelyStartDate instanceof Date
          ? preparedData.likelyStartDate.toISOString().split('T')[0]
          : (preparedData.likelyStartDate || new Date().toISOString().split('T')[0]),
        status: mapStatusToBackend(preparedData.status as OpportunityTrackingStatus || 'Bid Under Preparation'),
        currency: preparedData.currency || 'INR',
        capitalValue: preparedData.capitalValue || 0,
        durationOfProject: preparedData.durationOfProject || 0,
        fundingStream: preparedData.fundingStream || '',
        contractType: preparedData.contractType || '',
        bidFees: preparedData.bidFees || 0,
        emd: preparedData.emd || 0,
        formOfEMD: preparedData.formOfEMD,
        contactPersonAtClient: preparedData.contactPersonAtClient,
        dateOfSubmission: preparedData.dateOfSubmission instanceof Date
          ? preparedData.dateOfSubmission.toISOString().split('T')[0]
          : preparedData.dateOfSubmission,
        percentageChanceOfProjectHappening: preparedData.percentageChanceOfProjectHappening,
        percentageChanceOfNJSSuccess: preparedData.percentageChanceOfNJSSuccess,
        likelyCompetition: preparedData.likelyCompetition,
        grossRevenue: preparedData.grossRevenue || 0,
        netNJSRevenue: preparedData.netNJSRevenue || 0,
        followUpComments: preparedData.followUpComments,
        notes: preparedData.notes,
        probableQualifyingCriteria: preparedData.probableQualifyingCriteria,
        currentHistory: Array.isArray(preparedData.currentHistory)
          ? preparedData.currentHistory
          : preparedData.currentHistory ? [preparedData.currentHistory] : undefined,
        createdAt: preparedData.createdAt instanceof Date
          ? preparedData.createdAt.toISOString()
          : preparedData.createdAt,
        updatedAt: preparedData.updatedAt instanceof Date
          ? preparedData.updatedAt.toISOString()
          : preparedData.updatedAt,
        createdBy: preparedData.createdBy,
        updatedBy: preparedData.updatedBy
      };

      const response = await axiosInstance.post<OpportunityTracking>('api/OpportunityTracking', command);

      return normalizeOpportunityTracking(response.data) as OpportunityTracking;
    } catch (error) {
      console.error('Error creating opportunity:', error);
      throw error;
    }
  },

  sendToApproval: async (data: {
    opportunityId: number;
    approvalManagerId: string;
    action: string,
    comments?: string;
  }): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.post<BackendOpportunityTracking>('api/OpportunityTracking/SendToApproval', {
        opportunityId: data.opportunityId,
        assignedToId: data.approvalManagerId,
        comments: data.comments,
        action: data.action
      });

      return normalizeOpportunityTracking({
        ...response.data,
        stage: mapStageFromBackend(Number(response.data.stage)),
        status: mapStatusFromBackend(Number(response.data.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error sending opportunity for approval:', error);
      throw error;
    }
  },

  RejectByRegionManagerSentToBidManager: async (data: {
    opportunityId: number;
    approvalManagerId: string;
    action: string,
    comments?: string;
  }): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.post<BackendOpportunityTracking>('api/OpportunityTracking/Reject', {
        opportunityId: data.opportunityId,
        assignedToId: data.approvalManagerId,
        comments: data.comments,
        action: data.action
      });

      return normalizeOpportunityTracking({
        ...response.data,
        stage: mapStageFromBackend(Number(response.data.stage)),
        status: mapStatusFromBackend(Number(response.data.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error sending opportunity for approval:', error);
      throw error;
    }
  },
  sendToReview: async (data: {
    opportunityId: number;
    reviewManagerId: string;
    comments?: string;
  }): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.post<BackendOpportunityTracking>('api/OpportunityTracking/SendToReview', {
        opportunityId: data.opportunityId,
        assignedToId: data.reviewManagerId,
        comments: data.comments,
        action: 'SendToReview' // Added explicit action
      });

      return normalizeOpportunityTracking({
        ...response.data,
        stage: mapStageFromBackend(Number(response.data.stage)),
        status: mapStatusFromBackend(Number(response.data.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error sending opportunity for review:', error);
      throw error;
    }
  },


  sendToApprove: async (data: {
    opportunityId: number;
    approvalRegionalDirectorId: string;
    action: string,
    comments?: string;
  }): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.post<BackendOpportunityTracking>('api/OpportunityTracking/SendToApprove', {
        opportunityId: data.opportunityId,
        assignedToId: data.approvalRegionalDirectorId,
        comments: data.comments,
        action: data.action
      });

      return normalizeOpportunityTracking({
        ...response.data,
        stage: mapStageFromBackend(Number(response.data.stage)),
        status: mapStatusFromBackend(Number(response.data.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error sending opportunity for approval:', error);
      throw error;
    }
  },

  rejectOpportunityByRegionalDirector: async (data: {
    opportunityId: number;
    approvalRegionalDirectorId: string;
    action: string,
    comments?: string;
  }): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.post<BackendOpportunityTracking>('api/OpportunityTracking/SendToApprove', {
        opportunityId: data.opportunityId,
        assignedToId: data.approvalRegionalDirectorId,
        comments: data.comments,
        action: data.action
      });

      return normalizeOpportunityTracking({
        ...response.data,
        stage: mapStageFromBackend(Number(response.data.stage)),
        status: mapStatusFromBackend(Number(response.data.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error sending opportunity for approval:', error);
      throw error;
    }
  },

  getByUserId: async (userId: string): Promise<OpportunityTracking[]> => {
    try {
      const response = await axiosInstance.get<OpportunityTracking[]>(`api/OpportunityTracking/bid-manager/${userId}`);
      return response.data
        .map(opp => ({
          ...opp,
          stage: mapStageFromBackend(Number(opp.stage)),
          status: mapStatusFromBackend(Number(opp.status))
        }))
        .map(opp => normalizeOpportunityTracking(opp) as OpportunityTracking);
    } catch (error) {
      console.error('Error fetching opportunities by user ID:', error);
      throw error;
    }
  },

  getByReviewManagerId: async (reviewManagerId: string): Promise<OpportunityTracking[]> => {
    try {
      const response = await axiosInstance.get<OpportunityTracking[]>(`api/OpportunityTracking/regional-manager/${reviewManagerId}`);
      return response.data
        .map(opp => ({
          ...opp,
          stage: mapStageFromBackend(Number(opp.stage)),
          status: mapStatusFromBackend(Number(opp.status))
        }))
        .map(opp => normalizeOpportunityTracking(opp) as OpportunityTracking);
    } catch (error) {
      console.error('Error fetching opportunities by review manager ID:', error);
      throw error;
    }
  },

  getByApprovalManagerId: async (approvalManagerId: string): Promise<OpportunityTracking[]> => {
    try {
      const response = await axiosInstance.get<OpportunityTracking[]>(`api/OpportunityTracking/regional-director/${approvalManagerId}`);
      return response.data
        .map(opp => ({
          ...opp,
          stage: mapStageFromBackend(Number(opp.stage)),
          status: mapStatusFromBackend(Number(opp.status))
        }))
        .map(opp => normalizeOpportunityTracking(opp) as OpportunityTracking);
    } catch (error) {
      console.error('Error fetching opportunities by approval manager ID:', error);
      throw error;
    }
  },

  getAll: async (): Promise<OpportunityTracking[]> => {
    try {
      const response = await axiosInstance.get<BackendOpportunityTracking[]>('api/OpportunityTracking');
      return response.data.map(opp => ({
        ...opp,
        stage: mapStageFromBackend(Number(opp.stage)),
        status: mapStatusFromBackend(Number(opp.status))
      })).map(opp => normalizeOpportunityTracking(opp) as OpportunityTracking);
    } catch (error) {
      console.error('Error fetching all opportunities:', error);
      throw error;
    }
  },

  getById: async (opportunityId: number): Promise<OpportunityTracking> => {
    try {
      const response = await axiosInstance.get<BackendOpportunityTracking>(`api/OpportunityTracking/${opportunityId}`);
      const opp = response.data;
      return normalizeOpportunityTracking({
        ...opp,
        stage: mapStageFromBackend(Number(opp.stage)),
        status: mapStatusFromBackend(Number(opp.status))
      }) as OpportunityTracking;
    } catch (error) {
      console.error('Error fetching opportunity:', error);
      throw error;
    }
  },

  delete: async (opportunityId: number): Promise<void> => {
    try {
      await axiosInstance.delete(`/OpportunityTracking/${opportunityId}`);
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      throw error;
    }
  },

  getOpportunityByStatus: async (status: number | 0): Promise<OpportunityTracking[]> => {
    try {
      const response = await axiosInstance.get<BackendOpportunityTracking[]>(`api/OpportunityTracking?status=${status}`);
      return response.data
      .map(opp => ({
        ...opp,
        stage: mapStageFromBackend(Number(opp.stage)),
        status: mapStatusFromBackend(Number(opp.status))
      }))
      .map(opp => normalizeOpportunityTracking(opp) as OpportunityTracking);
    } catch (error) {
      console.error('Error fetching opportunity:', error);
      throw error;
    }
  },


};
