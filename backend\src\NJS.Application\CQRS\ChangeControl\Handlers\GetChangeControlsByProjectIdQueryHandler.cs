using MediatR;
using NJS.Application.CQRS.ChangeControl.Queries;
using NJS.Application.Dtos;
using NJS.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.ChangeControl.Handlers
{
    public class GetChangeControlsByProjectIdQueryHandler : IRequestHandler<GetChangeControlsByProjectIdQuery, IEnumerable<ChangeControlDto>>
    {
        private readonly IChangeControlRepository _changeControlRepository;

        public GetChangeControlsByProjectIdQueryHandler(IChangeControlRepository changeControlRepository)
        {
            _changeControlRepository = changeControlRepository ?? throw new ArgumentNullException(nameof(changeControlRepository));
        }

        public async Task<IEnumerable<ChangeControlDto>> Handle(GetChangeControlsByProjectIdQuery request, CancellationToken cancellationToken)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            var entities = await _changeControlRepository.GetByProjectIdAsync(request.ProjectId);
            
            return entities.Select(entity => new ChangeControlDto
            {
                Id = entity.Id,
                ProjectId = entity.ProjectId,
                SrNo = entity.SrNo,
                DateLogged = entity.DateLogged,
                Originator = entity.Originator,
                Description = entity.Description,
                CostImpact = entity.CostImpact,
                TimeImpact = entity.TimeImpact,
                ResourcesImpact = entity.ResourcesImpact,
                QualityImpact = entity.QualityImpact,
                ChangeOrderStatus = entity.ChangeOrderStatus,
                ClientApprovalStatus = entity.ClientApprovalStatus,
                ClaimSituation = entity.ClaimSituation,
                CreatedBy = entity.CreatedBy,
                UpdatedBy = entity.UpdatedBy,
                WorkflowHistory = entity.WorkflowHistories.OrderByDescending(x => x.ActionDate)
                .Select(history => new ChangeControlWorkflowHistoryDto
                {
                    Id = history.Id,
                    ChangeControlId = history.ChangeControlId,
                    ActionDate = history.ActionDate,
                    Comments = history.Comments,
                   /// Status = history.Status.Status,
                    StatusId = history.StatusId,
                    Action = history.Action,
                    ActionBy = history.ActionBy,
                    AssignedToId = history.AssignedToId

                })
                .FirstOrDefault()
              

            });
        }
    }
}
