﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{37251EA6-531B-4160-BD85-7BADCA5FF2D6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJSAPI", "src\NJSAPI\NJSAPI.csproj", "{A865093F-0B12-4FC2-8484-B86A1FB879E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJS.Domain", "src\NJS.Domain\NJS.Domain.csproj", "{091CB70B-844B-4683-A0C7-F2C7FD924307}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJS.Application", "src\NJS.Application\NJS.Application.csproj", "{ACB903AB-1E76-48FD-BB59-85E1A4805B9B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJS.Infrastructure", "NJS.Infrastructure\NJS.Infrastructure.csproj", "{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{908FF949-B417-4504-82EF-952427500FDE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJS.Domain.Tests", "NJS.Domain.Tests\NJS.Domain.Tests.csproj", "{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "NJS.Repositories", "NJS.Repositories\NJS.Repositories.csproj", "{FADF2B8E-A49C-4F5F-AF01-AF415DD76449}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NJS.API.Tests", "NJS.API.Tests\NJS.API.Tests.csproj", "{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A865093F-0B12-4FC2-8484-B86A1FB879E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A865093F-0B12-4FC2-8484-B86A1FB879E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A865093F-0B12-4FC2-8484-B86A1FB879E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A865093F-0B12-4FC2-8484-B86A1FB879E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{091CB70B-844B-4683-A0C7-F2C7FD924307}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{091CB70B-844B-4683-A0C7-F2C7FD924307}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{091CB70B-844B-4683-A0C7-F2C7FD924307}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{091CB70B-844B-4683-A0C7-F2C7FD924307}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACB903AB-1E76-48FD-BB59-85E1A4805B9B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACB903AB-1E76-48FD-BB59-85E1A4805B9B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACB903AB-1E76-48FD-BB59-85E1A4805B9B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACB903AB-1E76-48FD-BB59-85E1A4805B9B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51}.Release|Any CPU.Build.0 = Release|Any CPU
		{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADF2B8E-A49C-4F5F-AF01-AF415DD76449}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADF2B8E-A49C-4F5F-AF01-AF415DD76449}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADF2B8E-A49C-4F5F-AF01-AF415DD76449}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADF2B8E-A49C-4F5F-AF01-AF415DD76449}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD}.Release|Any CPU.Build.0 = Release|Any CPU

	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A865093F-0B12-4FC2-8484-B86A1FB879E1} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{091CB70B-844B-4683-A0C7-F2C7FD924307} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{ACB903AB-1E76-48FD-BB59-85E1A4805B9B} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{D789AC57-DF5D-4AFE-B8B1-BF208D34FA51} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{908FF949-B417-4504-82EF-952427500FDE} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{92F2CEF0-BF36-45B2-9896-A1E308E8B8D9} = {908FF949-B417-4504-82EF-952427500FDE}
		{FADF2B8E-A49C-4F5F-AF01-AF415DD76449} = {37251EA6-531B-4160-BD85-7BADCA5FF2D6}
		{8E98318D-0CF0-4E85-A35F-CF08C1ED88AD} = {908FF949-B417-4504-82EF-952427500FDE}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1C7B8BDB-5C63-4CDF-A8CC-2F49A423C289}
	EndGlobalSection
EndGlobal
