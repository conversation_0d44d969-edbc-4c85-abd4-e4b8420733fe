{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System.Net.Http.HttpClient": "Warning"}}, "Jwt": {"Key": "7bf578ef918fcccd26725d646385b72c95d29c01b38abc79caec1dbc4a36d2f5", "Issuer": "your-app-name", "Audience": "your-app-name"}, "AllowedHosts": "*", "ConnectionStrings": {"AppDbConnection": "Server=localhost;Database=KarmaTechAI_SAAS;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true;"}, "Cors": {"AllowedOrigins": ["http://localhost:5173", "http://localhost:5245", "http://localhost:5176", "http://**************:5179", "http://**************:5176"]}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "grstgibxcsxhjyrz", "FromEmail": "<EMAIL>", "FromName": "NJS Project Management", "EnableSsl": true, "EnableEmailNotifications": true}, "Swagger": {"Title": "NJS API", "Version": "1.10.4", "Description": "This is version 1.10.4 of the API", "Contact": {"Name": "Support", "Email": "<EMAIL>"}}}