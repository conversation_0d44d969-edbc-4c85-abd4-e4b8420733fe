import { OpportunityTracking } from "../../models"

// Raw opportunity tracking data
const opportunityTrackingRawData =  {
  "1":{
    "id": 1,
    "projectId": "usr2",
    "stage": "B",
    "strategicRanking": "H",
    "bidFees": 75000,
    "emd": 150000,
    "formOfEMD": "Bank Guarantee",
    "bidManagerId": "usr8",
    "reviewManagerId": undefined, 
    "approvalManagerId": undefined, 
    "contactPersonAtClient": "<PERSON><PERSON>",
    "dateOfSubmission": "2023-12-15",
    "percentageChanceOfProjectHappening": 75.5,
    "percentageChanceOfNJSSuccess": 65.0,
    "likelyCompetition": "L&T, HCC, Gammon",
    "grossRevenue": 7500000,
    "netNJSRevenue": 6000000,
    "followUpComments": "Client very interested in smart solutions",
    "notes": "Need to focus on IoT integration",
    "probableQualifyingCriteria": "Similar project experience, Local presence",
    "operation": "Mumbai",
    "workName": "Smart City Project",
    "client": "Mumbai Municipal Corporation", 
    "clientSector": "Government",
    "likelyStartDate": "2024-03-01",
    "status": "Bid Submitted",
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 36,
    "fundingStream": "Government Budget",
    "contractType": "EPC",
    "workflowId": "1"
  },
  "2":{
    "id": 2,
    "projectId": "usr3",
    "stage": "A",
    "strategicRanking": "M",
    "bidFees": 50000,
    "emd": 100000,
    "formOfEMD": "Bank Draft",
    "bidManagerId": "usr9",
    "reviewManagerId": undefined, 
    "approvalManagerId": undefined, 
    "contactPersonAtClient": "Amit Patel",
    "dateOfSubmission": "2023-12-30",
    "percentageChanceOfProjectHappening": 60.0,
    "percentageChanceOfNJSSuccess": 55.0,
    "likelyCompetition": "Tata Projects, SPML Infra",
    "grossRevenue": 3200000,
    "netNJSRevenue": 2500000,
    "followUpComments": "Technical presentation scheduled",
    "notes": "Focus on flood prediction systems",
    "probableQualifyingCriteria": "Similar project experience, Local presence",
    "operation": "Pune",
    "workName": "River Rejuvenation",
    "client": "Pune Municipal Corporation",
    "clientSector": "Government", 
    "likelyStartDate": "2024-06-01",
    "status": "Bid Under Preparation",
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 24,
    "fundingStream": "Government Grant",
    "contractType": "Item Rate",
    "workflowId": "2"
  },
  "3":{
    "id": 3,
    "projectId": "usr13",
    "stage": "B",
    "strategicRanking": "M",
    "bidFees": 50000,
    "emd": 100000,
    "formOfEMD": "Bank Draft",
    "bidManagerId": "usr8",
    "reviewManagerId": undefined, 
    "approvalManagerId": undefined, 
    "contactPersonAtClient": "Amita Patel",
    "dateOfSubmission": "2023-12-30",
    "percentageChanceOfProjectHappening": 60.0,
    "percentageChanceOfNJSSuccess": 55.0,
    "likelyCompetition": "Tata Projects, SPML Infra",
    "grossRevenue": 3200000,
    "netNJSRevenue": 2500000,
    "followUpComments": "Technical presentation scheduled",
    "notes": "Focus on flood prediction systems",
    "probableQualifyingCriteria": "Similar project experience, Local presence",
    "operation": "Nagpur",
    "workName": "Sewage Treatment Plant",
    "client": "Nagpur Municipal Corporation",
    "clientSector": "Government",
    "likelyStartDate": "2024-04-01", 
    "status": "Bid Submitted",
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 30,
    "fundingStream": "Multilateral Funding",
    "contractType": "Lump Sum",
    "workflowId": "3"
  },
  "4":{
    "id": 4,
    "projectId": "usr15",
    "stage": "A",
    "strategicRanking": "M",
    "bidFees": 50000,
    "emd": 100000,
    "formOfEMD": "Bank Draft",
    "bidManagerId": "usr9",
    "reviewManagerId": undefined, 
    "approvalManagerId": undefined, 
    "contactPersonAtClient": "Amit Patel",
    "dateOfSubmission": "2023-12-30",
    "percentageChanceOfProjectHappening": 60.0,
    "percentageChanceOfNJSSuccess": 55.0,
    "likelyCompetition": "Tata Projects, SPML Infra, ABC",
    "grossRevenue": 3200000,
    "netNJSRevenue": 2500000,
    "followUpComments": "Technical presentation scheduled",
    "notes": "Focus on flood prediction systems",
    "probableQualifyingCriteria": "Similar project experience, Local presence",
    "operation": "Nashik",
    "workName": "Water Supply Scheme",
    "client": "Nashik Municipal Corporation", 
    "clientSector": "Government",
    "likelyStartDate": "2024-08-01",
    "status": "Bid Under Preparation", 
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 48,
    "fundingStream": "Government Budget",
    "contractType": "EPC",
    "workflowId": "4"
  },
  "5":{
    "id": 5,
    "projectId": "usr16",
    "stage": "A",
    "strategicRanking": "H",
    "bidFees": 60000,
    "emd": 120000,
    "formOfEMD": "Bank Guarantee",
    "bidManagerId": "usr8",
    "reviewManagerId": "usr6", 
    "approvalManagerId": "usr15", 
    "contactPersonAtClient": "Suresh Singh",
    "dateOfSubmission": "2024-02-28",
    "percentageChanceOfProjectHappening": 70.0,
    "percentageChanceOfNJSSuccess": 60.0,
    "likelyCompetition": "Tata Projects, L&T",
    "grossRevenue": 4500000,
    "netNJSRevenue": 3500000,
    "followUpComments": "Initial discussions positive",
    "notes": "Focus on sustainable solutions",
    "probableQualifyingCriteria": "Technical expertise, Past experience",
    "operation": "Delhi",
    "workName": "Metro Water Project",
    "client": "Delhi Metro Corporation",
    "clientSector": "Government",
    "likelyStartDate": "2024-05-01",
    "status": "Bid Under Preparation",
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 24,
    "fundingStream": "Government Budget",
    "contractType": "EPC",
    "workflowId": "5"
  },
  "6":{
    "id": 6,
    "projectId": "usr2",
    "stage": "B",
    "strategicRanking": "H",
    "bidFees": 75000,
    "emd": 150000,
    "formOfEMD": "Bank Guarantee",
    "bidManagerId": "usr8",
    "reviewManagerId": undefined, 
    "approvalManagerId": undefined, 
    "contactPersonAtClient": "Rajesh Kumar",
    "dateOfSubmission": "2023-12-15",
    "percentageChanceOfProjectHappening": 75.5,
    "percentageChanceOfNJSSuccess": 65.0,
    "likelyCompetition": "L&T, HCC, Gammon",
    "grossRevenue": 7500000,
    "netNJSRevenue": 6000000,
    "followUpComments": "Client very interested in smart solutions",
    "notes": "Need to focus on IoT integration",
    "probableQualifyingCriteria": "Similar project experience, Local presence",
    "operation": "Mumbai",
    "workName": "Super Smart City Project",
    "client": "Mumbai Municipal Corporation", 
    "clientSector": "Government",
    "likelyStartDate": "2024-03-01",
    "status": "Bid Submitted",
    "currency": "INR",
    "capitalValue": *********,
    "durationOfProject": 36,
    "fundingStream": "Government Budget",
    "contractType": "EPC",
    "workflowId": "1"
  },
} as const;

// Transform into typed array
export const opportunityTrackings: OpportunityTracking[] = Object.values(opportunityTrackingRawData).map(tracking => ({
  id: tracking.id,
  projectId: tracking.projectId,
  stage: tracking.stage,
  strategicRanking: tracking.strategicRanking,
  bidFees: tracking.bidFees,
  emd: tracking.emd,
  formOfEMD: tracking.formOfEMD,
  bidManagerId: tracking.bidManagerId,
  reviewManagerId: tracking.reviewManagerId,
  approvalManagerId: tracking.approvalManagerId,
  contactPersonAtClient: tracking.contactPersonAtClient,
  dateOfSubmission: tracking.dateOfSubmission,
  percentageChanceOfProjectHappening: tracking.percentageChanceOfProjectHappening,
  percentageChanceOfNJSSuccess: tracking.percentageChanceOfNJSSuccess,
  likelyCompetition: tracking.likelyCompetition,
  grossRevenue: tracking.grossRevenue,
  netNJSRevenue: tracking.netNJSRevenue,
  followUpComments: tracking.followUpComments,
  notes: tracking.notes,
  probableQualifyingCriteria: tracking.probableQualifyingCriteria,
  operation: tracking.operation,
  workName: tracking.workName,
  client: tracking.client,
  clientSector: tracking.clientSector,
  likelyStartDate: tracking.likelyStartDate,
  status: tracking.status,
  currency: tracking.currency,
  capitalValue: tracking.capitalValue,
  durationOfProject: tracking.durationOfProject,
  fundingStream: tracking.fundingStream,
  contractType: tracking.contractType,
  workflowId: tracking.workflowId
}));

// Utility functions
export const getOpportunityById = (id: string | number): OpportunityTracking | undefined => {
  return opportunityTrackings.find(opportunity => String(opportunity.id) === String(id));
};

export const getOpportunityByProjectId = (projectId: string): OpportunityTracking | undefined => {
  return opportunityTrackings.find(opportunity => opportunity.projectId === String(projectId));
};

export const getOpportunitiesByStage = (stage: string): OpportunityTracking[] => {
  return opportunityTrackings.filter(opportunity => opportunity.stage === String(stage));
};

export const getOpportunitiesByStrategicRanking = (ranking: string): OpportunityTracking[] => {
  return opportunityTrackings.filter(opportunity => opportunity.strategicRanking === String(ranking));
};

export const getOpportunitiesByBidManager = (bidManagerId: string): OpportunityTracking[] => {
  return opportunityTrackings.filter(opportunity => opportunity.bidManagerId === String(bidManagerId));
};

export const getOpportunitiesByWorkflowId = (workflowId: string): OpportunityTracking[] => {
  return opportunityTrackings.filter(opportunity => opportunity.workflowId === String(workflowId));
};

// Updated utility function to get opportunities by review manager
export const getOpportunitiesByReviewManager = (reviewManagerId: string): OpportunityTracking[] => {
  return opportunityTrackings.filter(opportunity => opportunity.reviewManagerId === String(reviewManagerId));
};
