// export const scoringDescriptions = {
//     "marketingplan": {
//         "high": "Fits well with marketing strategy",
//         "medium": "Fits somewhat into the marketing strategy",
//         "low": "Does not fit with marketing strategy"
//     },
//     "clientrelationship": {
//         "high": "Excellent relationships, no past problem projects",
//         "medium": "Fair/good relationships, some project problems",
//         "low": "Strained relationship(s), problem project(s), selectability questionable"
//     },
//     "projectknowledge": {
//         "high": "Strategic project, excellent knowledge of project development",
//         "medium": "Known about project, but some knowledge of project development",
//         "low": "Knew nothing about project prior to receipt of RFQ/RFP"
//     },
//     "technicaleligibility": {
//         "high": "Meets all criteria on its own",
//         "medium": "Need of JV or some support to meet the criteria",
//         "low": "Does not meet qualification criteria"
//     },
//     "financialeligibility": {
//         "high": "Meets all criteria on its own",
//         "medium": "Need of JV or some support to meet the criteria",
//         "low": "Does not meet qualification criteria"
//     },
//     "keystaffavailability": {
//         "high": "All competent key staff available",
//         "medium": "Most competent key staff available but some outsourcing required",
//         "low": "Major outsourcing required"
//     },
//     "projectcompetition": {
//         "high": "NJS has inside track, and competition is manageable",
//         "medium": "NJS faces formidable competition, and have limited intelligence on it",
//         "low": "Project appears to be wired for competition"
//     },
//     "competitionposition": {
//         "high": "NJS qualifications are technically superior",
//         "medium": "Qualifications are equivalent to competition, or we may have a slight edge",
//         "low": "NJS qualifications are lower to the competition"
//     },
//     "futureworkpotential": {
//         "high": "Project will lead to future work",
//         "medium": "Possible future work",
//         "low": "One-time project, no future work"
//     },
//     "projectprofitability": {
//         "high": "Good profit potential",
//         "medium": "Competitive pricing, Moderate potential profit",
//         "low": "Risky and may lead to little/no profit"
//     },
//     "projectschedule": {
//         "high": "More than adequate, project will not adversely impact other projects",
//         "medium": "Adequate, other projects may be adversely impacted",
//         "low": "Not adequate, other projects will be adversely impacted"
//     },
//     "bidtimeandcosts": {
//         "high": "Favorable",
//         "medium": "Reasonable",
//         "low": "Constrained"
//     }
// } as const;

// export type ScoringDescriptions = typeof scoringDescriptions;
