# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
    - name: List root directory
      run: ls -R    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x
    - name: Restore dependencies
      run: dotnet restore backend/src/NJSAPI/NJSAPI.csproj
    - name: Build
      run: dotnet build backend/src/NJSAPI/NJSAPI.csproj --no-restore
    - name: Test
      run: dotnet test backend/src/NJSAPI/NJSAPI.csproj --no-build --verbosity normal
