namespace NJS.Domain.Models;

public class EmailSettings
{
    public const string SectionName = "EmailSettings";
    public string SmtpServer { get; set; } = string.Empty;
    public int Port { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public bool EnableSsl { get; set; }
    public bool EnableEmailNotifications { get; set; }
}
