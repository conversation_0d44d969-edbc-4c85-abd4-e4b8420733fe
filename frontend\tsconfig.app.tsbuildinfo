{"root": ["./src/app.tsx", "./src/main.tsx", "./src/types.tsx", "./src/vite-env.d.ts", "./src/api/changecontrolapi.ts", "./src/api/checkreviewapi.ts", "./src/api/inputregisterapi.ts", "./src/api/pmworkflowapi.tsx", "./src/components/alertspanel.tsx", "./src/components/dashboard.test.tsx", "./src/components/dashboard.tsx", "./src/components/loadingspinner.tsx", "./src/components/pagination.test.tsx", "./src/components/pagination.tsx", "./src/components/reportslist.test.tsx", "./src/components/reportslist.tsx", "./src/components/resourcemanagement.test.tsx", "./src/components/resourcemanagement.tsx", "./src/components/wbschart.test.tsx", "./src/components/wbschart.tsx", "./src/components/adminpanel/rolesmanagement.tsx", "./src/components/adminpanel/usersmanagement.tsx", "./src/components/common/bdchips.test.tsx", "./src/components/common/bdchips.tsx", "./src/components/common/bargraphpopup.tsx", "./src/components/common/changecontrolworkflow.tsx", "./src/components/common/formsection.tsx", "./src/components/common/loadingbutton.tsx", "./src/components/common/loadingspinner.test.tsx", "./src/components/common/loadingspinner.tsx", "./src/components/common/opportunitytrackingworkflow.test.tsx", "./src/components/common/opportunitytrackingworkflow.tsx", "./src/components/common/projecttrackingworkflow.tsx", "./src/components/common/sectionsummaryrow.tsx", "./src/components/common/tabletemplate.tsx", "./src/components/dashboard/businessdevelopmentcharts.tsx", "./src/components/dashboard/projectstatuspiechart.tsx", "./src/components/dialogbox/decideapproval.tsx", "./src/components/dialogbox/decidereview.tsx", "./src/components/dialogbox/deletewbsdialog.tsx", "./src/components/dialogbox/sendforapproval.tsx", "./src/components/dialogbox/sendforreview.tsx", "./src/components/dialogbox/index.tsx", "./src/components/dialogbox/projectreviewworkflow/projectdecideapproval.tsx", "./src/components/dialogbox/projectreviewworkflow/projectdecidereview.tsx", "./src/components/dialogbox/projectreviewworkflow/projectsendforapproval.tsx", "./src/components/dialogbox/projectreviewworkflow/projectsendforreview.tsx", "./src/components/dialogbox/projectreviewworkflow/reviewbox.tsx", "./src/components/dialogbox/projectreviewworkflow/sendapprovalbox.tsx", "./src/components/dialogbox/adminpage/roledialog.tsx", "./src/components/dialogbox/adminpage/userdialog.tsx", "./src/components/dialogbox/changecontrol/decideapproval.tsx", "./src/components/dialogbox/changecontrol/decidereview.tsx", "./src/components/dialogbox/changecontrol/sendforapproval.tsx", "./src/components/dialogbox/changecontrol/sendforreview.tsx", "./src/components/dialogbox/changecontrol/index.ts", "./src/components/forms/bidpreparationform.tsx", "./src/components/forms/bidversionhistory.tsx", "./src/components/forms/changecontrolform.tsx", "./src/components/forms/checkreviewform.tsx", "./src/components/forms/correspondenceform.tsx", "./src/components/forms/feasibilitystudyform.tsx", "./src/components/forms/formwrapper.tsx", "./src/components/forms/formsoverview.tsx", "./src/components/forms/gonogoapprovalstatus.tsx", "./src/components/forms/gonogoform.tsx", "./src/components/forms/gonogoversionhistory.tsx", "./src/components/forms/inputregisterform.tsx", "./src/components/forms/jobstartform.tsx", "./src/components/forms/monthlyprogressform.tsx", "./src/components/forms/opportunityform.test.tsx", "./src/components/forms/opportunityform.tsx", "./src/components/forms/projectclosureform.tsx", "./src/components/forms/projectinitform.tsx", "./src/components/forms/workbreakdownstructureform.tsx", "./src/components/forms/index.tsx", "./src/components/forms/changecontrolcomponents/changecontroldialog.tsx", "./src/components/forms/checkreviewcomponents/checkreviewdialog.tsx", "./src/components/forms/correspondancecomponents/correspondencedialog.tsx", "./src/components/forms/inputregisterformcomponents/inputregisterdialog.tsx", "./src/components/forms/monthlyprogresscomponents/actionstab.tsx", "./src/components/forms/monthlyprogresscomponents/budgetandscheduletab.tsx", "./src/components/forms/monthlyprogresscomponents/changeorderstab.tsx", "./src/components/forms/monthlyprogresscomponents/contractandcoststab.tsx", "./src/components/forms/monthlyprogresscomponents/financialdetailstab.tsx", "./src/components/forms/monthlyprogresscomponents/inputfield.tsx", "./src/components/forms/monthlyprogresscomponents/manpowerplanningtab.tsx", "./src/components/forms/monthlyprogresscomponents/index.tsx", "./src/components/forms/wbsformcomponents/levelselect.tsx", "./src/components/forms/wbsformcomponents/wbsheader.tsx", "./src/components/forms/wbsformcomponents/wbsrow.tsx", "./src/components/forms/wbsformcomponents/wbssummary.tsx", "./src/components/forms/wbsformcomponents/wbstable.tsx", "./src/components/forms/jobstartformcomponent/estimatedexpenses.tsx", "./src/components/forms/jobstartformcomponent/jobstartformheader.tsx", "./src/components/forms/jobstartformcomponent/jobstartgrandtotal.tsx", "./src/components/forms/jobstartformcomponent/jobstartsummary.tsx", "./src/components/forms/jobstartformcomponent/jobstarttime.tsx", "./src/components/forms/jobstartformcomponent/tabletemplate.tsx", "./src/components/forms/jobstartformcomponent/jobstarttable.tsx", "./src/components/navigation/navbar.test.tsx", "./src/components/navigation/navbar.tsx", "./src/components/navigation/notificationcenter.test.tsx", "./src/components/navigation/notificationcenter.tsx", "./src/components/projects/decideapprovaldialog.tsx", "./src/components/projects/decidereviewdialog.tsx", "./src/components/projects/opportunityitem.tsx", "./src/components/projects/opportunitylist.tsx", "./src/components/projects/pmworkflowbutton.tsx", "./src/components/projects/projectfilter.tsx", "./src/components/projects/projectinitializationdialog.tsx", "./src/components/projects/projectitem.tsx", "./src/components/projects/projectmanagement.tsx", "./src/components/projects/projectmanagementprojectlist.tsx", "./src/components/projects/sendforreviewdialog.tsx", "./src/components/projects/workflowhistorydisplay.tsx", "./src/components/projects/__tests__/pmworkflowbutton.test.tsx", "./src/components/projects/projectclosure/projectclosurelist.tsx", "./src/components/widgets/businessdevelopmentwidget.tsx", "./src/components/widgets/decisionwidget.tsx", "./src/components/widgets/gonogowidget.tsx", "./src/components/widgets/historywidget.tsx", "./src/components/widgets/notificationsnackbar.tsx", "./src/components/widgets/projectheaderwidget.tsx", "./src/context/loadingcontext.tsx", "./src/dummyapi/api.tsx", "./src/dummyapi/authapi.tsx", "./src/dummyapi/axiosconfig.tsx", "./src/dummyapi/bidpreparationapi.tsx", "./src/dummyapi/bidversionhistoryapi.tsx", "./src/dummyapi/changecontrolapi.tsx", "./src/dummyapi/checkreviewapi.tsx", "./src/dummyapi/correspondenceapi.tsx", "./src/dummyapi/dummyopportunityhistoryapi.tsx", "./src/dummyapi/gonogoapi.tsx", "./src/dummyapi/inputregisterapi.tsx", "./src/dummyapi/opportunityapi.test.tsx", "./src/dummyapi/opportunityapi.tsx", "./src/dummyapi/opportunityworkflowapi.tsx", "./src/dummyapi/projectapi.test.tsx", "./src/dummyapi/projectapi.tsx", "./src/dummyapi/projectclosureapi.tsx", "./src/dummyapi/rolesapi.tsx", "./src/dummyapi/scoringdescriptionsdata.ts", "./src/dummyapi/usersapi.tsx", "./src/dummyapi/wbsapi.tsx", "./src/dummyapi/workflowapi.tsx", "./src/dummyapi/database/dummychangecontrol.tsx", "./src/dummyapi/database/dummycheckreview.tsx", "./src/dummyapi/database/dummycorrespondence.tsx", "./src/dummyapi/database/dummyinputregister.tsx", "./src/dummyapi/database/dummyopportunityhistory.tsx", "./src/dummyapi/database/dummyopporunityworkflow.tsx", "./src/dummyapi/database/dummyprojectclosure.tsx", "./src/dummyapi/database/dummyprojects.tsx", "./src/dummyapi/database/dummyresourceroles.tsx", "./src/dummyapi/database/dummyroles.tsx", "./src/dummyapi/database/dummywbstasks.tsx", "./src/dummyapi/database/dummygonogo.tsx", "./src/dummyapi/database/dummyopportunitytracking.tsx", "./src/dummyapi/database/dummyusers.tsx", "./src/hooks/usepmworkflow.tsx", "./src/hooks/useroles.tsx", "./src/hooks/useusers.tsx", "./src/hooks/useworkflow.tsx", "./src/models/gonogodecisionopportunitymodel.tsx", "./src/models/changecontrolmodel.tsx", "./src/models/checkreviewmodel.tsx", "./src/models/employeemodel.tsx", "./src/models/gonogodecisionmodel.test.tsx", "./src/models/gonogodecisionmodel.tsx", "./src/models/gonogoversionmodel.tsx", "./src/models/index.tsx", "./src/models/inputregisterrowmodel.tsx", "./src/models/inwardrowmodel.tsx", "./src/models/monthlyhourmodel.tsx", "./src/models/monthlyreviewmodel.tsx", "./src/models/opportunityhistorymodel.tsx", "./src/models/opportunitystagemodel.tsx", "./src/models/opportunitytrackingmodel.test.tsx", "./src/models/opportunitytrackingmodel.tsx", "./src/models/opportunitytrackingstatusmodel.tsx", "./src/models/outwardrowmodel.tsx", "./src/models/permissiontypemodel.tsx", "./src/models/pmworkflowmodel.tsx", "./src/models/projectclosurecommentmodel.tsx", "./src/models/projectclosurerowmodel.tsx", "./src/models/projectmodel.tsx", "./src/models/resourcerolemodel.tsx", "./src/models/roledefinitionmodel.tsx", "./src/models/rolemodel.tsx", "./src/models/types.tsx", "./src/models/usermodel.tsx", "./src/models/userrolemodel.tsx", "./src/models/wbstaskmodel.tsx", "./src/models/wbstaskresourceallocationmodel.tsx", "./src/models/workflowentrymodel.tsx", "./src/models/workflowmodel.tsx", "./src/models/workflowstatusmodel.tsx", "./src/pages/adminpanel.tsx", "./src/pages/businessdevelopment.tsx", "./src/pages/businessdevelopmentdashboard.tsx", "./src/pages/businessdevelopmentdetails.tsx", "./src/pages/home.tsx", "./src/pages/loginscreen.tsx", "./src/pages/projectclosure.tsx", "./src/pages/projectdetails.tsx", "./src/pages/projectmanagement.tsx", "./src/pages/roles.tsx", "./src/pages/users.tsx", "./src/pages/index.tsx", "./src/services/api.tsx", "./src/services/authapi.tsx", "./src/services/axiosconfig.tsx", "./src/services/changecontrolapi.tsx", "./src/services/correspondenceapi.ts", "./src/services/directupdateapi.ts", "./src/services/fallbackdata.tsx", "./src/services/feasibilitystudyapi.tsx", "./src/services/gonogoapi.tsx", "./src/services/gonogoopportunityapi.tsx", "./src/services/historyloggingservice.tsx", "./src/services/jobstartformapi.ts", "./src/services/jobstartformheaderapi.tsx", "./src/services/opportunityapi.tsx", "./src/services/projectapi.tsx", "./src/services/projectclosureapi.ts", "./src/services/resourceapi.tsx", "./src/services/rolesapi.tsx", "./src/services/scoringdescriptionapi.tsx", "./src/services/userapi.tsx", "./src/services/wbsapi.tsx", "./src/services/wbsheaderapi.tsx", "./src/services/wbsworkflowapi.tsx", "./src/types/auth.ts", "./src/types/index.tsx", "./src/types/jobstartform.ts", "./src/types/jobstartformtypes.ts", "./src/types/wbs.tsx", "./src/utils/calculations.ts", "./src/utils/jobstartformstyles.ts", "./src/utils/jobstartformutils.ts", "./src/utils/statusutils.test.tsx", "./src/utils/statusutils.tsx"], "errors": true, "version": "5.7.2"}