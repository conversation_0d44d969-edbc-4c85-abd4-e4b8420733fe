import { axiosInstance } from './axiosConfig';

// Define TypeScript interfaces for API response
export interface MonthlyHourDto {
  year: number;
  month: string;
  plannedHours: number;
}

export interface ManpowerResourceDto {
  taskId: string;
  taskTitle: string;
  employeeId: string;
  employeeName: string;
  roleId: string;
  isConsultant: boolean;
  costRate: number;
  totalHours: number;
  totalCost: number;
  monthlyHours: MonthlyHourDto[];
}

export interface ManpowerResourcesResponse {
  projectId: number;
  resources: ManpowerResourceDto[];
}

export const MonthlyProgressAPI = {
  /**
   * Get manpower resources with monthly hours for a project
   * @param projectId Project ID
   * @returns Promise with manpower resources data
   */
  getManpowerResources: async (projectId: string): Promise<ManpowerResourcesResponse> => {
    try {
      const response = await axiosInstance.get(`/api/projects/${projectId}/WBS/manpowerresources`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching manpower resources for project ${projectId}:`, error);
      throw new Error(`Failed to load manpower resources for project ${projectId}.`);
    }
  },

  /**
   * Get monthly progress records for a project
   * @param projectId Project ID
   * @returns Promise with monthly progress data
   */
  getMonthlyProgressByProjectId: async (projectId: string) => {
    try {
      const response = await axiosInstance.get(`/api/projects/${projectId}/monthlyprogress`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching monthly progress for project ${projectId}:`, error);
      throw new Error(`Failed to load monthly progress for project ${projectId}.`);
    }
  },

  /**
   * Get monthly progress record by ID
   * @param id Monthly progress ID
   * @returns Promise with monthly progress data
   */
  getMonthlyProgressById: async (id: string) => {
    try {
      const response = await axiosInstance.get(`/api/monthlyprogress/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching monthly progress with ID ${id}:`, error);
      throw new Error(`Failed to load monthly progress with ID ${id}.`);
    }
  },

  /**
   * Create monthly progress form data
   * @param projectId Project ID
   * @param formData Monthly progress form data
   * @returns Promise with creation result
   */
  createMonthlyProgress: async (projectId: string, formData: any) => {
    try {
      const response = await axiosInstance.post(`/api/projects/${projectId}/monthlyprogress`, formData);
      return response.data;
    } catch (error) {
      console.error(`Error creating monthly progress for project ${projectId}:`, error);
      throw new Error(`Failed to create monthly progress for project ${projectId}.`);
    }
  },

  /**
   * Update monthly progress form data
   * @param id Monthly progress ID
   * @param formData Monthly progress form data
   * @returns Promise with update result
   */
  updateMonthlyProgress: async (id: string, formData: any) => {
    try {
      const response = await axiosInstance.put(`/api/monthlyprogress/${id}`, formData);
      return response.data;
    } catch (error) {
      console.error(`Error updating monthly progress with ID ${id}:`, error);
      throw new Error(`Failed to update monthly progress with ID ${id}.`);
    }
  },

  /**
   * Delete monthly progress record
   * @param id Monthly progress ID
   * @returns Promise with deletion result
   */
  deleteMonthlyProgress: async (id: string) => {
    try {
      const response = await axiosInstance.delete(`/api/monthlyprogress/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting monthly progress with ID ${id}:`, error);
      throw new Error(`Failed to delete monthly progress with ID ${id}.`);
    }
  },

  /**
   * Submit monthly progress form data (legacy method for backward compatibility)
   * @param projectId Project ID
   * @param formData Monthly progress form data
   * @returns Promise with submission result
   */
  submitMonthlyProgress: async (projectId: string, formData: any) => {
    return MonthlyProgressAPI.createMonthlyProgress(projectId, formData);
  }
};
