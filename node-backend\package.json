{"name": "backend", "version": "1.0.0", "description": "NJS Project Management Backend", "main": "src/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.13", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^22.10.5", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}